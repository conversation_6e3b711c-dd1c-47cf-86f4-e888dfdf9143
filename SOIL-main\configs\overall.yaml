# general
gpu_id: 0
use_gpu: True
seed: [999]

# multi-modal raw features
data_path: './data/'
inter_splitting_label: 'x_label'
filter_out_cod_start_users: True
is_multimodal_model: True

checkpoint_dir: 'saved'
save_recommended_topk: True
recommend_topk: 'recommend_topk/'

embedding_size: 64
weight_decay: 0.0
req_training: True
#embedding_size: 3780

# training settings
epochs: 1000
stopping_step: 10
train_batch_size: 2048
learner: adam
learning_rate: 0.001
learning_rate_scheduler: [1.0, 50]
eval_step: 1

training_neg_sample_num: 1
use_neg_sampling: True
use_full_sampling: False
NEG_PREFIX: neg__

USER_ID_FIELD: user_id:token
ITEM_ID_FIELD: item_id:token
TIME_FIELD: ~
#TIME_FIELD: timestamp:float
field_separator: "\t"


# evaluation settings
metrics: ["Recall", "NDCG", "Precision", "MAP"]
topk: [5, 10, 20, 50]
valid_metric: Recall@20
eval_batch_size: 2048

#
use_raw_features: False
max_txt_len: 32
max_img_size: 256
vocab_size: 30522
type_vocab_size: 2
hidden_size: 4
pad_token_id: 0
max_position_embeddings: 512
layer_norm_eps: 1e-12
hidden_dropout_prob: 0.1

end2end: False

# iteration parameters
hyper_parameters: ["seed"]
