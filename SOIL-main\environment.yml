name: soil
channels:
  - pyg
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch
  - http://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - blas=1.0=mkl
  - bottleneck=1.3.5=py37hda87dfa_0
  - brotlipy=0.7.0=py37h540881e_1004
  - bzip2=1.0.8=h7f98852_4
  - ca-certificates=2023.01.10=h06a4308_0
  - certifi=2022.12.7=py37h06a4308_0
  - cffi=1.14.6=py37hc58025e_0
  - charset-normalizer=3.1.0=pyhd8ed1ab_0
  - cryptography=38.0.2=py37h38fbfac_1
  - cudatoolkit=11.3.1=h9edb442_11
  - cycler=0.11.0=pyhd8ed1ab_0
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.12.1=hca18f0e_1
  - gmp=6.2.1=h58526e2_0
  - gnutls=3.6.13=h85f3911_1
  - icu=67.1=he1b5a44_0
  - idna=3.4=pyhd8ed1ab_0
  - intel-openmp=2022.1.0=h9e868ea_3769
  - joblib=1.1.1=py37h06a4308_0
  - jpeg=9e=h0b41bf4_3
  - kiwisolver=1.4.4=py37h7cecad7_0
  - lame=3.100=h166bdaf_1003
  - lcms2=2.14=h6ed2654_0
  - ld_impl_linux-64=2.40=h41732ed_0
  - lerc=4.0.0=h27087fc_0
  - libblas=3.9.0=16_linux64_mkl
  - libcblas=3.9.0=16_linux64_mkl
  - libdeflate=1.14=h166bdaf_0
  - libffi=3.3=h58526e2_2
  - libgcc-ng=12.2.0=h65d4601_19
  - libgfortran-ng=12.2.0=h69a702a_19
  - libgfortran5=12.2.0=h337968e_19
  - libgomp=12.2.0=h65d4601_19
  - libiconv=1.17=h166bdaf_0
  - liblapack=3.9.0=16_linux64_mkl
  - libopenblas=0.3.21=pthreads_h78a6416_3
  - libpng=1.6.39=h753d276_0
  - libsqlite=3.40.0=h753d276_0
  - libstdcxx-ng=12.2.0=h46fd767_19
  - libtiff=4.4.0=h82bc61c_5
  - libuv=1.44.2=h166bdaf_0
  - libwebp-base=1.3.0=h0b41bf4_0
  - libxcb=1.13=h7f98852_1004
  - libzlib=1.2.13=h166bdaf_4
  - matplotlib=3.2.2=1
  - matplotlib-base=3.2.2=py37h1d35a4c_1
  - mkl=2022.1.0=hc2b9512_224
  - ncurses=6.3=h27087fc_1
  - nettle=3.6=he412f7d_0
  - numexpr=2.7.3=py37he8f5f7f_1
  - numpy=1.21.5=py37h976b520_1
  - openh264=2.1.1=h780b84a_0
  - openjpeg=2.5.0=h7d73246_1
  - openssl=1.1.1t=h7f8727e_0
  - packaging=23.0=pyhd8ed1ab_0
  - pandas=1.3.5=py37h8c16a72_0
  - pillow=9.2.0=py37h850a105_2
  - pip=23.0.1=pyhd8ed1ab_0
  - pthread-stubs=0.4=h36c2ea0_1001
  - pycparser=2.21=pyhd8ed1ab_0
  - pyopenssl=23.1.1=pyhd8ed1ab_0
  - pyparsing=3.0.9=pyhd8ed1ab_0
  - pysocks=1.7.1=py37h89c1867_5
  - python=3.7.11=h12debd9_0
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python_abi=3.7=2_cp37m
  - pytorch=1.11.0=py3.7_cuda11.3_cudnn8.2.0_0
  - pytorch-mutex=1.0=cuda
  - pytorch-scatter=2.0.9=py37_torch_1.11.0_cu113
  - pytz=2023.3=pyhd8ed1ab_0
  - pyyaml=6.0=py37h540881e_4
  - readline=8.2=h8228510_1
  - requests=2.28.2=pyhd8ed1ab_1
  - scikit-learn=1.0.2=py37h51133e4_1
  - scipy=1.7.3=py37hf2a6cf1_0
  - setuptools=67.6.1=pyhd8ed1ab_0
  - six=1.16.0=pyh6c4a22f_0
  - sqlite=3.40.0=h4ff8645_0
  - threadpoolctl=2.2.0=pyh0d69192_0
  - tk=8.6.12=h27826a3_0
  - torchaudio=0.11.0=py37_cu113
  - torchvision=0.12.0=py37_cu113
  - tornado=6.2=py37h540881e_0
  - typing-extensions=4.5.0=hd8ed1ab_0
  - typing_extensions=4.5.0=pyha770c72_0
  - urllib3=1.26.15=pyhd8ed1ab_0
  - wheel=0.40.0=pyhd8ed1ab_0
  - xorg-libxau=1.0.9=h7f98852_0
  - xorg-libxdmcp=1.1.3=h7f98852_0
  - xz=5.2.6=h166bdaf_0
  - yaml=0.2.5=h7f98852_2
  - zlib=1.2.13=h166bdaf_4
  - zstd=1.5.2=h3eb15da_6
  - pip:
      - absl-py==1.4.0
      - cachetools==5.3.1
      - et-xmlfile==1.1.0
      - google-auth==2.22.0
      - google-auth-oauthlib==0.4.6
      - grpcio==1.56.0
      - importlib-metadata==6.7.0
      - jinja2==3.1.3
      - lmdb==1.4.1
      - markdown==3.4.3
      - markupsafe==2.1.3
      - oauthlib==3.2.2
      - openpyxl==3.1.2
      - protobuf==3.20.3
      - psutil==5.9.8
      - pyasn1==0.5.0
      - pyasn1-modules==0.3.0
      - requests-oauthlib==1.3.1
      - rsa==4.9
      - tensorboard==2.11.2
      - tensorboard-data-server==0.6.1
      - tensorboard-plugin-wit==1.8.1
      - torch-geometric==2.3.1
      - tqdm==4.65.0
      - werkzeug==2.2.3
      - zipp==3.15.0
prefix: /home/<USER>/miniconda3/envs/soil
