# coding: utf-8

"""
属性数据处理工具
##########################
"""

import os
import pandas as pd
import numpy as np
from collections import defaultdict


def create_dummy_attribute_data(dataset_path, dataset_name, n_items):
    """
    为没有属性数据的数据集创建虚拟属性数据
    
    Args:
        dataset_path: 数据集路径
        dataset_name: 数据集名称
        n_items: 物品数量
    """
    attr_file = os.path.join(dataset_path, 'item_attribute_label.csv')
    
    if os.path.exists(attr_file):
        print(f"Attribute file already exists: {attr_file}")
        return
    
    print(f"Creating dummy attribute data for {dataset_name} dataset...")
    
    # 创建虚拟属性数据
    np.random.seed(42)  # 确保可重复性
    
    data = []
    for item_id in range(n_items):
        # 生成随机属性标签
        price_label = np.random.randint(0, 5)  # 价格等级 0-4
        salesrank_label = np.random.randint(0, 5)  # 销售排名等级 0-4
        
        if dataset_name == 'baby':
            brand_label = np.random.randint(0, 663)  # Baby数据集品牌数
            category_label = 0  # Baby数据集没有类别
        elif dataset_name == 'sports':
            brand_label = np.random.randint(0, 2081)  # Sports数据集品牌数
            category_label = np.random.randint(0, 18)  # Sports数据集类别数
        else:  # clothing或其他
            brand_label = np.random.randint(0, 1288)  # 默认品牌数
            category_label = np.random.randint(0, 19)  # 默认类别数
        
        data.append({
            'itemID': item_id,
            'price_label': price_label,
            'salesrank_label': salesrank_label,
            'brand_label': brand_label,
            'category_label': category_label
        })
    
    # 保存为CSV文件
    df = pd.DataFrame(data)
    df.to_csv(attr_file, index=False)
    print(f"Created dummy attribute data with {len(data)} items: {attr_file}")


def validate_attribute_data(dataset_path, dataset_name):
    """
    验证属性数据的有效性
    
    Args:
        dataset_path: 数据集路径
        dataset_name: 数据集名称
    
    Returns:
        bool: 数据是否有效
    """
    attr_file = os.path.join(dataset_path, 'item_attribute_label.csv')
    
    if not os.path.exists(attr_file):
        print(f"Attribute file not found: {attr_file}")
        return False
    
    try:
        df = pd.read_csv(attr_file)
        required_columns = ['itemID', 'price_label', 'salesrank_label', 'brand_label']
        if dataset_name != 'baby':
            required_columns.append('category_label')
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"Missing columns in attribute file: {missing_columns}")
            return False
        
        print(f"Attribute data validation passed: {len(df)} items")
        return True
        
    except Exception as e:
        print(f"Error validating attribute data: {e}")
        return False


def analyze_attribute_distribution(dataset_path):
    """
    分析属性数据的分布情况
    
    Args:
        dataset_path: 数据集路径
    """
    attr_file = os.path.join(dataset_path, 'item_attribute_label.csv')
    
    if not os.path.exists(attr_file):
        print(f"Attribute file not found: {attr_file}")
        return
    
    try:
        df = pd.read_csv(attr_file)
        print(f"\n=== Attribute Data Analysis ===")
        print(f"Total items: {len(df)}")
        
        for col in ['price_label', 'salesrank_label', 'brand_label', 'category_label']:
            if col in df.columns:
                unique_values = df[col].nunique()
                value_counts = df[col].value_counts().head(10)
                print(f"\n{col}:")
                print(f"  Unique values: {unique_values}")
                print(f"  Top 10 values:")
                for val, count in value_counts.items():
                    print(f"    {val}: {count}")
        
    except Exception as e:
        print(f"Error analyzing attribute data: {e}")


def prepare_attribute_data_for_training(dataset_path, dataset_name, n_items):
    """
    为训练准备属性数据
    
    Args:
        dataset_path: 数据集路径
        dataset_name: 数据集名称
        n_items: 物品数量
    
    Returns:
        bool: 准备是否成功
    """
    print(f"Preparing attribute data for {dataset_name} dataset...")
    
    # 检查是否存在属性文件
    if not validate_attribute_data(dataset_path, dataset_name):
        print("Creating dummy attribute data...")
        create_dummy_attribute_data(dataset_path, dataset_name, n_items)
        
        # 再次验证
        if not validate_attribute_data(dataset_path, dataset_name):
            print("Failed to create valid attribute data")
            return False
    
    # 分析属性分布
    analyze_attribute_distribution(dataset_path)
    
    print("Attribute data preparation completed successfully!")
    return True


if __name__ == "__main__":
    # 测试代码
    import sys
    if len(sys.argv) >= 4:
        dataset_path = sys.argv[1]
        dataset_name = sys.argv[2]
        n_items = int(sys.argv[3])
        prepare_attribute_data_for_training(dataset_path, dataset_name, n_items)
    else:
        print("Usage: python attribute_utils.py <dataset_path> <dataset_name> <n_items>")
