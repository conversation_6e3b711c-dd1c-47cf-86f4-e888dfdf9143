# coding: utf-8

"""
增强版SOIL训练脚本
##########################
"""

import os
import argparse
from utils.quick_start import quick_start
from utils.attribute_utils import prepare_attribute_data_for_training

os.environ['NUMEXPR_MAX_THREADS'] = '48'


def main():
    parser = argparse.ArgumentParser(description='Train Enhanced SOIL Model')
    parser.add_argument('--model', '-m', type=str, default='SOIL', help='name of models')
    parser.add_argument('--dataset', '-d', type=str, default='baby', help='name of datasets')
    parser.add_argument('--config', '-c', type=str, default='SOIL', help='config file name (default: SOIL)')
    parser.add_argument('--enable_attr', action='store_true', help='enable attribute learning')
    parser.add_argument('--prepare_attr', action='store_true', help='prepare attribute data')

    args, _ = parser.parse_known_args()

    config_dict = {
        'gpu_id': 0,
    }

    # 如果指定启用属性学习，覆盖配置并调整超参数列表
    if args.enable_attr:
        config_dict['enable_attribute_learning'] = True
        # 动态添加属性学习相关的超参数到调优列表
        config_dict['hyper_parameters'] = ["cl_loss", "cl_loss2", "attr_loss_weight", "factor_cl_weight"]
        print("Attribute learning enabled via command line argument")
        print("Added attribute learning hyperparameters to tuning list")

    # 准备属性数据
    if args.prepare_attr or args.enable_attr:
        dataset_path = os.path.abspath(f'data/{args.dataset}')

        # 估算物品数量（这里使用一个简单的方法，实际应该从数据集中读取）
        try:
            inter_file = os.path.join(dataset_path, f'{args.dataset}.inter')
            if os.path.exists(inter_file):
                with open(inter_file, 'r') as f:
                    lines = f.readlines()
                    max_item_id = 0
                    for line in lines[1:]:  # 跳过头部
                        parts = line.strip().split('\t')
                        if len(parts) >= 2:
                            try:
                                item_id = int(parts[1])
                                max_item_id = max(max_item_id, item_id)
                            except:
                                continue
                    n_items = max_item_id + 1
            else:
                print(f"Warning: {inter_file} not found, using default item count")
                n_items = 1000  # 默认值
        except Exception as e:
            print(f"Error estimating item count: {e}, using default")
            n_items = 1000

        print(f"Preparing attribute data for {args.dataset} with {n_items} items...")
        prepare_attribute_data_for_training(dataset_path, args.dataset, n_items)

    # 使用指定的配置文件（默认使用SOIL.yaml）
    config_dict['config_file_list'] = [f'configs/model/{args.config}.yaml']

    print(f"Training {args.model} on {args.dataset} dataset with config {args.config}")
    print(f"Config overrides: {config_dict}")

    # 开始训练
    quick_start(model=args.model, dataset=args.dataset, config_dict=config_dict, save_model=True)


if __name__ == '__main__':
    main()
