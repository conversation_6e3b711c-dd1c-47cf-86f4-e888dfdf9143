#!/usr/bin/env python3
"""
Enhanced SOIL 快速优化脚本
基于性能分析报告的优化建议进行快速实验
"""

import os
import sys
import yaml
import subprocess
import time
from datetime import datetime
import pandas as pd

class QuickOptimizer:
    def __init__(self):
        self.base_config = "configs/model/SOIL.yaml"
        self.results = []
        self.best_result = None
        
    def load_base_config(self):
        """加载基础配置"""
        with open(self.base_config, 'r') as f:
            return yaml.safe_load(f)
    
    def create_experiment_config(self, params):
        """创建实验配置"""
        config = self.load_base_config()
        
        # 更新优化参数
        config.update(params)
        
        # 设置快速实验参数
        config['epochs'] = 30  # 快速实验只运行30个epoch
        config['stopping_step'] = 10
        config['enable_attribute_learning'] = True
        
        return config
    
    def run_experiment(self, params, exp_name):
        """运行单个实验"""
        print(f"\n🚀 开始实验: {exp_name}")
        print(f"参数: {params}")
        
        # 创建配置
        config = self.create_experiment_config(params)
        
        # 保存临时配置文件
        temp_config_path = f"temp_config_{exp_name}.yaml"
        with open(temp_config_path, 'w') as f:
            yaml.dump(config, f)
        
        # 构建命令
        cmd = [
            "python", "main.py",
            "-m", "SOIL",
            "-d", "baby",
            "--enable_attr",
            "--config_file", temp_config_path
        ]
        
        start_time = time.time()
        
        try:
            # 运行实验
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
            
            if result.returncode == 0:
                # 解析结果
                recall20 = self.parse_result(result.stdout)
                duration = time.time() - start_time
                
                exp_result = {
                    'experiment': exp_name,
                    'params': params,
                    'recall20': recall20,
                    'duration': duration,
                    'status': 'success'
                }
                
                print(f"✅ 实验完成: Recall@20 = {recall20:.4f}, 用时 {duration:.1f}s")
                
            else:
                exp_result = {
                    'experiment': exp_name,
                    'params': params,
                    'recall20': 0.0,
                    'duration': time.time() - start_time,
                    'status': 'failed',
                    'error': result.stderr
                }
                print(f"❌ 实验失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            exp_result = {
                'experiment': exp_name,
                'params': params,
                'recall20': 0.0,
                'duration': 1800,
                'status': 'timeout'
            }
            print(f"⏰ 实验超时")
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_config_path):
                os.remove(temp_config_path)
        
        self.results.append(exp_result)
        
        # 更新最佳结果
        if exp_result['status'] == 'success':
            if self.best_result is None or exp_result['recall20'] > self.best_result['recall20']:
                self.best_result = exp_result
                print(f"🏆 新的最佳结果: {exp_result['recall20']:.4f}")
        
        return exp_result
    
    def parse_result(self, output):
        """从输出中解析Recall@20结果"""
        lines = output.split('\n')
        for line in lines:
            if 'test result:' in line and 'recall@20:' in line:
                # 提取recall@20的值
                parts = line.split('recall@20:')
                if len(parts) > 1:
                    value_part = parts[1].split()[0]
                    try:
                        return float(value_part)
                    except ValueError:
                        continue
        return 0.0
    
    def run_phase1_experiments(self):
        """第一阶段：超参数快速扫描"""
        print("\n" + "="*60)
        print("🔬 第一阶段：超参数快速扫描")
        print("="*60)
        
        # 定义实验参数组合
        experiments = [
            # 基准实验 (当前配置)
            {
                'name': 'baseline',
                'params': {
                    'attr_loss_weight': [0.05],
                    'factor_cl_weight': [0.01],
                    'learning_rate': 0.001
                }
            },
            
            # 增加属性损失权重
            {
                'name': 'high_attr_weight_1',
                'params': {
                    'attr_loss_weight': [0.2],
                    'factor_cl_weight': [0.01],
                    'learning_rate': 0.001
                }
            },
            {
                'name': 'high_attr_weight_2',
                'params': {
                    'attr_loss_weight': [0.3],
                    'factor_cl_weight': [0.01],
                    'learning_rate': 0.001
                }
            },
            {
                'name': 'high_attr_weight_3',
                'params': {
                    'attr_loss_weight': [0.5],
                    'factor_cl_weight': [0.01],
                    'learning_rate': 0.001
                }
            },
            
            # 增加因子对比学习权重
            {
                'name': 'high_factor_weight_1',
                'params': {
                    'attr_loss_weight': [0.2],
                    'factor_cl_weight': [0.1],
                    'learning_rate': 0.001
                }
            },
            {
                'name': 'high_factor_weight_2',
                'params': {
                    'attr_loss_weight': [0.2],
                    'factor_cl_weight': [0.15],
                    'learning_rate': 0.001
                }
            },
            
            # 调整学习率
            {
                'name': 'low_lr',
                'params': {
                    'attr_loss_weight': [0.2],
                    'factor_cl_weight': [0.1],
                    'learning_rate': 0.0005
                }
            },
            {
                'name': 'high_lr',
                'params': {
                    'attr_loss_weight': [0.2],
                    'factor_cl_weight': [0.1],
                    'learning_rate': 0.002
                }
            },
            
            # 最佳组合猜测
            {
                'name': 'best_guess',
                'params': {
                    'attr_loss_weight': [0.3],
                    'factor_cl_weight': [0.15],
                    'learning_rate': 0.0005,
                    'n_factors': 3
                }
            }
        ]
        
        # 运行实验
        for exp in experiments:
            self.run_experiment(exp['params'], exp['name'])
            time.sleep(5)  # 短暂休息
    
    def save_results(self):
        """保存实验结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果
        df = pd.DataFrame(self.results)
        results_file = f"optimization_results_{timestamp}.csv"
        df.to_csv(results_file, index=False)
        
        # 生成报告
        report_file = f"optimization_report_{timestamp}.md"
        with open(report_file, 'w') as f:
            f.write("# Enhanced SOIL 快速优化实验报告\n\n")
            f.write(f"实验时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 实验结果汇总\n\n")
            f.write("| 实验名称 | Recall@20 | 状态 | 用时(s) |\n")
            f.write("|----------|-----------|------|--------|\n")
            
            for result in sorted(self.results, key=lambda x: x.get('recall20', 0), reverse=True):
                f.write(f"| {result['experiment']} | {result['recall20']:.4f} | {result['status']} | {result['duration']:.1f} |\n")
            
            if self.best_result:
                f.write(f"\n## 最佳结果\n\n")
                f.write(f"- **实验名称**: {self.best_result['experiment']}\n")
                f.write(f"- **Recall@20**: {self.best_result['recall20']:.4f}\n")
                f.write(f"- **参数配置**: {self.best_result['params']}\n")
                f.write(f"- **相对基准提升**: {((self.best_result['recall20'] / 0.0906) - 1) * 100:.1f}%\n")
                f.write(f"- **相对原版差距**: {((self.best_result['recall20'] / 0.0967) - 1) * 100:.1f}%\n")
        
        print(f"\n📊 结果已保存:")
        print(f"  - 详细数据: {results_file}")
        print(f"  - 实验报告: {report_file}")
    
    def print_summary(self):
        """打印实验总结"""
        print("\n" + "="*60)
        print("📈 实验总结")
        print("="*60)
        
        successful_results = [r for r in self.results if r['status'] == 'success']
        
        if successful_results:
            best = max(successful_results, key=lambda x: x['recall20'])
            worst = min(successful_results, key=lambda x: x['recall20'])
            avg_recall = sum(r['recall20'] for r in successful_results) / len(successful_results)
            
            print(f"成功实验数量: {len(successful_results)}/{len(self.results)}")
            print(f"最佳结果: {best['recall20']:.4f} ({best['experiment']})")
            print(f"最差结果: {worst['recall20']:.4f} ({worst['experiment']})")
            print(f"平均性能: {avg_recall:.4f}")
            print(f"基准性能: 0.0906 (当前Enhanced SOIL)")
            print(f"目标性能: 0.0967 (原始SOIL)")
            
            if best['recall20'] > 0.0906:
                improvement = ((best['recall20'] / 0.0906) - 1) * 100
                print(f"✅ 相对当前版本提升: +{improvement:.1f}%")
            
            if best['recall20'] >= 0.0967:
                print(f"🎉 已达到原版性能水平!")
            else:
                gap = ((0.0967 / best['recall20']) - 1) * 100
                print(f"📊 距离原版性能还差: {gap:.1f}%")
        else:
            print("❌ 没有成功的实验结果")

def main():
    print("🔬 Enhanced SOIL 快速优化实验")
    print("基于性能分析报告的优化建议")
    print("="*60)
    
    optimizer = QuickOptimizer()
    
    try:
        # 运行第一阶段实验
        optimizer.run_phase1_experiments()
        
        # 打印总结
        optimizer.print_summary()
        
        # 保存结果
        optimizer.save_results()
        
        print("\n🎯 下一步建议:")
        if optimizer.best_result and optimizer.best_result['recall20'] > 0.092:
            print("  1. 使用最佳参数进行完整训练 (100+ epochs)")
            print("  2. 尝试两阶段训练策略")
            print("  3. 测试模型简化选项")
        else:
            print("  1. 尝试更大的权重值")
            print("  2. 检查数据质量")
            print("  3. 考虑模型架构调整")
            
    except KeyboardInterrupt:
        print("\n⏹️ 实验被用户中断")
        optimizer.save_results()
    except Exception as e:
        print(f"\n❌ 实验出错: {e}")
        optimizer.save_results()

if __name__ == "__main__":
    main()
