# coding: utf-8


import os
import argparse
from utils.quick_start import quick_start
from utils.attribute_utils import prepare_attribute_data_for_training
os.environ['NUMEXPR_MAX_THREADS'] = '48'


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Train SOIL Model with Optional Attribute Learning')
    parser.add_argument('--model', '-m', type=str, default='SOIL', help='name of models')
    parser.add_argument('--dataset', '-d', type=str, default='baby', help='name of datasets')
    parser.add_argument('--enable_attr', action='store_true', help='enable attribute learning')
    parser.add_argument('--prepare_attr', action='store_true', help='prepare attribute data')

    config_dict = {
        'gpu_id': 0,
    }

    args, _ = parser.parse_known_args()

    # 如果指定启用属性学习，覆盖配置并调整超参数列表
    if args.enable_attr:
        config_dict['enable_attribute_learning'] = True
        # 动态添加属性学习相关的超参数到调优列表
        config_dict['hyper_parameters'] = ["cl_loss", "cl_loss2", "attr_loss_weight", "factor_cl_weight"]
        print("Attribute learning enabled via command line argument")
        print("Added attribute learning hyperparameters to tuning list")

    # 准备属性数据（如果需要）
    if args.prepare_attr or args.enable_attr:
        try:
            dataset_path = f"./data/{args.dataset}"
            # 获取物品数量（这里使用一个估计值，实际应该从数据集中读取）
            n_items = 7050 if args.dataset == 'baby' else 10000  # 默认值
            prepare_attribute_data_for_training(dataset_path, args.dataset, n_items)
        except Exception as e:
            print(f"Warning: Failed to prepare attribute data: {e}")
            print("Continuing with existing data...")

    print(f"Training {args.model} on {args.dataset} dataset")
    if args.enable_attr:
        print("Enhanced SOIL with attribute learning enabled")
    else:
        print("Original SOIL model")
    print(f"Config overrides: {config_dict}")

    quick_start(model=args.model, dataset=args.dataset, config_dict=config_dict, save_model=True)


