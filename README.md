# SOIL Multimodal Recommendation System

This repository contains the SOIL (Second-Order Interest Learning) multimodal recommendation system implementation.

## Project Structure

- `SOIL-main/` - Main SOIL project directory
  - `models/` - Model implementations
  - `utils/` - Utility functions
  - `configs/` - Configuration files
  - `data/` - Dataset files (CSV and interaction files)
  - `common/` - Common modules

## Features

- Multimodal recommendation using image and text features
- Second-order interest learning
- Contrastive learning framework
- Support for multiple datasets (Baby, Clothing, Sports)

## Usage

See `SOIL-main/USAGE_GUIDE.md` for detailed usage instructions.

## Note

Large data files (.npy, .pt) are excluded from this repository. Please generate them using the provided scripts.
