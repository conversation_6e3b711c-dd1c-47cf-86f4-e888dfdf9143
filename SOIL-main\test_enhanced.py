# coding: utf-8

"""
Enhanced SOIL 测试脚本
##########################
"""

import torch
import numpy as np
from models.soil import SOIL
from utils.attribute_utils import create_dummy_attribute_data
import os
import tempfile


class MockDataset:
    """模拟数据集类"""
    def __init__(self, n_users=100, n_items=200):
        self.n_users = n_users
        self.n_items = n_items

        # 创建模拟交互矩阵
        from scipy.sparse import coo_matrix
        np.random.seed(42)
        rows = np.random.randint(0, n_users, 1000)
        cols = np.random.randint(0, n_items, 1000)
        data = np.ones(1000)
        self.interaction_matrix = coo_matrix((data, (rows, cols)), shape=(n_users, n_items))

        # 创建模拟历史交互
        self.history_items_per_u = {}
        for u in range(n_users):
            items = np.random.choice(n_items, size=np.random.randint(5, 20), replace=False)
            self.history_items_per_u[u] = items.tolist()

    def inter_matrix(self, form='coo'):
        return self.interaction_matrix

    def get_user_num(self):
        return self.n_users

    def get_item_num(self):
        return self.n_items


class MockDataLoader:
    """模拟数据加载器类"""
    def __init__(self, dataset):
        self.dataset = dataset


def test_original_soil():
    """测试原始SOIL功能"""
    print("Testing original SOIL functionality...")

    # 创建配置
    config = {
        'embedding_size': 32,
        'n_ui_layers': 1,
        'n_layers': 1,
        'cl_loss': 0.1,
        'cl_loss2': 0.1,
        'reg_weight': 1e-4,
        'knn_k': 5,
        'knn_i': 5,
        'knn_a': 5,
        'data_path': 'data/',
        'dataset': 'test',
        'enable_attribute_learning': False,  # 关闭属性学习
        'device': 'cpu',
        # 添加必要的字段
        'USER_ID_FIELD': 'user_id',
        'ITEM_ID_FIELD': 'item_id',
        'NEG_PREFIX': 'neg_',
        'train_batch_size': 32,
        'end2end': True,
        'is_multimodal_model': False
    }

    # 创建模拟数据集
    dataset = MockDataset(n_users=50, n_items=100)
    dataloader = MockDataLoader(dataset)

    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        config['data_path'] = temp_dir + '/'
        os.makedirs(os.path.join(temp_dir, 'test'), exist_ok=True)

        # 创建模型
        model = SOIL(config, dataloader)
        model.eval()

        # 测试前向传播
        with torch.no_grad():
            user_embeds, item_embeds = model.forward(model.norm_adj, train=False)
            print(f"User embeddings shape: {user_embeds.shape}")
            print(f"Item embeddings shape: {item_embeds.shape}")

        # 测试损失计算
        users = torch.randint(0, 50, (10,))
        pos_items = torch.randint(0, 100, (10,))
        neg_items = torch.randint(0, 100, (10,))
        interaction = [users, pos_items, neg_items]

        loss = model.calculate_loss(interaction)
        print(f"Loss: {loss.item()}")

        print("Original SOIL test passed!")


def test_enhanced_soil():
    """测试增强版SOIL功能"""
    print("\nTesting enhanced SOIL functionality...")

    # 创建配置
    config = {
        'embedding_size': 32,
        'n_ui_layers': 1,
        'n_layers': 1,
        'cl_loss': 0.1,
        'cl_loss2': 0.1,
        'reg_weight': 1e-4,
        'knn_k': 5,
        'knn_i': 5,
        'knn_a': 5,
        'data_path': 'data/',
        'dataset': 'test',
        'enable_attribute_learning': True,  # 启用属性学习
        'n_factors': 4,
        'attr_loss_weight': 0.1,
        'factor_cl_weight': 0.05,
        'temperature': 1.0,
        'device': 'cpu',
        # 添加必要的字段
        'USER_ID_FIELD': 'user_id',
        'ITEM_ID_FIELD': 'item_id',
        'NEG_PREFIX': 'neg_',
        'train_batch_size': 32,
        'end2end': True,
        'is_multimodal_model': False
    }

    # 创建模拟数据集
    dataset = MockDataset(n_users=50, n_items=100)
    dataloader = MockDataLoader(dataset)

    # 创建临时目录和属性数据
    with tempfile.TemporaryDirectory() as temp_dir:
        config['data_path'] = temp_dir + '/'
        dataset_path = os.path.join(temp_dir, 'test')
        os.makedirs(dataset_path, exist_ok=True)

        # 创建虚拟属性数据
        create_dummy_attribute_data(dataset_path, 'test', 100)

        # 创建模型
        model = SOIL(config, dataloader)
        model.eval()

        # 测试前向传播（训练模式）
        model.train()
        forward_result = model.forward(model.norm_adj, train=True)
        print(f"Forward result length: {len(forward_result)}")

        if len(forward_result) == 9:
            (ua_embeddings, ia_embeddings, side_embeds, content_embeds,
             user_factors, item_factors, text_factors, visual_factors, fused_factors) = forward_result
            print(f"User embeddings shape: {ua_embeddings.shape}")
            print(f"Item embeddings shape: {ia_embeddings.shape}")
            print(f"Number of user factors: {len(user_factors)}")
            print(f"Number of item factors: {len(item_factors)}")
            print(f"Number of fused factors: {len(fused_factors)}")

        # 测试损失计算
        users = torch.randint(0, 50, (10,))
        pos_items = torch.randint(0, 100, (10,))
        neg_items = torch.randint(0, 100, (10,))
        interaction = [users, pos_items, neg_items]

        loss = model.calculate_loss(interaction)
        print(f"Enhanced loss: {loss.item()}")

        # 测试推理模式
        model.eval()
        with torch.no_grad():
            user_embeds, item_embeds = model.forward(model.norm_adj, train=False)
            print(f"Inference - User embeddings shape: {user_embeds.shape}")
            print(f"Inference - Item embeddings shape: {item_embeds.shape}")

        print("Enhanced SOIL test passed!")


def test_compatibility():
    """测试兼容性"""
    print("\nTesting compatibility...")

    # 测试启用属性学习但没有属性数据的情况
    config = {
        'embedding_size': 32,
        'n_ui_layers': 1,
        'n_layers': 1,
        'cl_loss': 0.1,
        'cl_loss2': 0.1,
        'reg_weight': 1e-4,
        'knn_k': 5,
        'knn_i': 5,
        'knn_a': 5,
        'data_path': 'data/',
        'dataset': 'test',
        'enable_attribute_learning': True,  # 启用但没有数据
        'n_factors': 4,
        'attr_loss_weight': 0.1,
        'factor_cl_weight': 0.05,
        'temperature': 1.0,
        'device': 'cpu',
        # 添加必要的字段
        'USER_ID_FIELD': 'user_id',
        'ITEM_ID_FIELD': 'item_id',
        'NEG_PREFIX': 'neg_',
        'train_batch_size': 32,
        'end2end': True,
        'is_multimodal_model': False
    }

    dataset = MockDataset(n_users=50, n_items=100)
    dataloader = MockDataLoader(dataset)

    with tempfile.TemporaryDirectory() as temp_dir:
        config['data_path'] = temp_dir + '/'
        os.makedirs(os.path.join(temp_dir, 'test'), exist_ok=True)
        # 不创建属性数据

        model = SOIL(config, dataloader)

        # 测试损失计算（应该回退到原始模式）
        users = torch.randint(0, 50, (10,))
        pos_items = torch.randint(0, 100, (10,))
        neg_items = torch.randint(0, 100, (10,))
        interaction = [users, pos_items, neg_items]

        loss = model.calculate_loss(interaction)
        print(f"Compatibility loss (no attr data): {loss.item()}")

        print("Compatibility test passed!")


if __name__ == "__main__":
    print("Running Enhanced SOIL tests...")

    try:
        test_original_soil()
        test_enhanced_soil()
        test_compatibility()
        print("\n✅ All tests passed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
