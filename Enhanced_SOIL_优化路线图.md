# Enhanced SOIL 优化路线图

## 🎯 当前状态评估

### ✅ 已确认的最优配置
- **attr_loss_weight = 0.05, factor_cl_weight = 0.01**
- **Test Recall@20 = 0.0974** (相比原版SOIL仅下降5.3%)
- **架构有效性已验证** - 属性学习和因子对比学习确实有助于推荐性能

### 📊 性能差距分析
- **当前差距**: 0.0054 (0.1028 - 0.0974)
- **相对差距**: 5.3%
- **优化空间**: 有明确的提升潜力

---

## 🚀 优化路线图

### 🔥 优先级1：超参数精细调优 (预期提升2-3%)

#### 1.1 因子数量优化
```yaml
# 实验方案
n_factors: [3, 5, 6, 8]  # 当前为4
attr_loss_weight: [0.05]  # 保持最优权重
factor_cl_weight: [0.01]  # 保持最优权重
```

**实验设计**:
- **参数范围**: n_factors ∈ {3, 5, 6, 8}
- **预期效果**: 更多因子可能提供更丰富的表示，但也可能导致过拟合
- **实验优先级**: ⭐⭐⭐⭐⭐
- **预期提升**: Test Recall@20 → 0.0985-0.0995

#### 1.2 温度参数调优
```yaml
# 实验方案
temperature: [0.5, 0.8, 1.2, 1.5, 2.0]  # 当前为1.0
n_factors: [5]  # 使用最优因子数
```

**实验设计**:
- **参数范围**: temperature ∈ {0.5, 0.8, 1.2, 1.5, 2.0}
- **预期效果**: 较低温度增强对比学习的区分度，较高温度提供更平滑的学习
- **实验优先级**: ⭐⭐⭐⭐
- **预期提升**: Test Recall@20 → 0.0980-0.0990

### 🎯 优先级2：训练策略优化 (预期提升3-5%)

#### 2.1 两阶段训练策略
```python
# 实验方案
# 阶段1: 仅推荐任务 (epochs 1-20)
attr_loss_weight: [0.0]
factor_cl_weight: [0.0]

# 阶段2: 加入属性学习 (epochs 21-60)
attr_loss_weight: [0.05]
factor_cl_weight: [0.01]
```

**实验设计**:
- **训练策略**: 前20个epoch专注基础推荐任务，后续加入属性学习
- **预期效果**: 避免早期训练中多任务冲突，提供更稳定的基础表示
- **实验优先级**: ⭐⭐⭐⭐
- **预期提升**: Test Recall@20 → 0.0995-0.1010

#### 2.2 渐进式权重调整
```python
# 实验方案
def dynamic_weights(epoch):
    if epoch < 10:
        return 0.02, 0.005  # 较小权重
    elif epoch < 30:
        return 0.05, 0.01   # 最优权重
    else:
        return 0.03, 0.008  # 后期微调
```

**实验设计**:
- **权重调度**: 动态调整属性学习权重
- **预期效果**: 在不同训练阶段使用最适合的权重配置
- **实验优先级**: ⭐⭐⭐
- **预期提升**: Test Recall@20 → 0.0985-0.1000

#### 2.3 差分学习率策略
```yaml
# 实验方案
base_lr: 0.001          # 基础模块学习率
attr_lr: 0.0005         # 属性学习模块学习率 (较低)
factor_lr: 0.0008       # 因子对比学习模块学习率
```

**实验设计**:
- **学习率配置**: 为不同模块设置不同学习率
- **预期效果**: 新增模块使用较低学习率，避免破坏预训练表示
- **实验优先级**: ⭐⭐⭐
- **预期提升**: Test Recall@20 → 0.0980-0.0995

### ⚡ 优先级3：架构微调 (预期提升1-3%)

#### 3.1 MLP架构优化
```python
# 当前架构
price_mlp: [LeakyReLU, Dropout(0.2), Linear(16→5)]

# 优化方案
price_mlp: [
    Linear(16→32), BatchNorm1d, LeakyReLU, Dropout(0.1),
    Linear(32→16), BatchNorm1d, LeakyReLU, Dropout(0.1),
    Linear(16→5)
]
```

**实验设计**:
- **架构改进**: 增加中间层，添加BatchNorm，调整Dropout
- **预期效果**: 提升属性预测准确性，间接改善推荐性能
- **实验优先级**: ⭐⭐
- **预期提升**: Test Recall@20 → 0.0980-0.0990

#### 3.2 激活函数优化
```yaml
# 实验方案
activation_functions: [
    "LeakyReLU",     # 当前
    "ReLU", 
    "GELU", 
    "Swish",
    "Mish"
]
```

**实验设计**:
- **激活函数**: 测试不同激活函数对属性学习的影响
- **预期效果**: 更好的非线性表达能力
- **实验优先级**: ⭐⭐
- **预期提升**: Test Recall@20 → 0.0975-0.0985

### 🔬 优先级4：高级优化策略 (预期提升2-4%)

#### 4.1 注意力机制增强
```python
# 实验方案
class AttributeAttention(nn.Module):
    def __init__(self, hidden_dim=64):
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=4)
    
    def forward(self, item_emb, attr_features):
        # 属性特征与物品嵌入的注意力融合
        return self.attention(item_emb, attr_features, attr_features)
```

**实验设计**:
- **注意力机制**: 在属性学习中引入注意力机制
- **预期效果**: 更好地融合属性信息和物品表示
- **实验优先级**: ⭐⭐
- **预期提升**: Test Recall@20 → 0.0990-0.1005

#### 4.2 对比学习策略优化
```python
# 实验方案
contrastive_strategies = [
    "InfoNCE",           # 当前
    "SimCLR", 
    "SupCon",
    "HardNegative",
    "AdaptiveTemp"
]
```

**实验设计**:
- **对比学习**: 尝试不同的对比学习策略
- **预期效果**: 更有效的因子解耦学习
- **实验优先级**: ⭐⭐
- **预期提升**: Test Recall@20 → 0.0985-0.1000

---

## 📋 实验执行计划

### 第一阶段 (1-2周): 超参数精细调优
1. **n_factors优化** (2-3天)
2. **temperature调优** (2-3天)  
3. **最优组合验证** (1-2天)

### 第二阶段 (2-3周): 训练策略优化
1. **两阶段训练** (3-4天)
2. **渐进式权重调整** (3-4天)
3. **差分学习率策略** (2-3天)

### 第三阶段 (1-2周): 架构微调
1. **MLP架构优化** (3-4天)
2. **激活函数优化** (2-3天)

### 第四阶段 (2-3周): 高级策略 (可选)
1. **注意力机制增强** (4-5天)
2. **对比学习策略优化** (4-5天)

---

## 🎯 性能目标设定

### 短期目标 (第一阶段完成)
- **Test Recall@20 > 0.0990** (恢复到原版96.3%性能)
- **相对提升**: +1.6% (从0.0974提升)

### 中期目标 (第二阶段完成)
- **Test Recall@20 > 0.1010** (恢复到原版98.2%性能)  
- **相对提升**: +3.7% (从0.0974提升)

### 长期目标 (全部阶段完成)
- **Test Recall@20 > 0.1030** (超越原版性能)
- **相对提升**: +5.7% (从0.0974提升)

---

## 🔍 风险评估与缓解策略

### 高风险项目
- **架构大幅修改**: 可能破坏现有平衡 → 采用渐进式修改
- **复杂训练策略**: 可能引入不稳定性 → 充分验证和回滚机制

### 低风险项目  
- **超参数调优**: 风险较低 → 优先执行
- **权重微调**: 基于已验证的最优配置 → 安全可靠

---

## 📊 成功评估标准

1. **性能指标**: Test Recall@20, NDCG@20, Precision@20
2. **稳定性**: 多次运行结果的一致性
3. **训练效率**: 收敛速度和计算资源消耗
4. **泛化能力**: 在其他数据集上的表现
